"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { hasPermission, permissionConfig } from '@/config/permissions'
import PageLoading from '@/components/layout/page-loading'

interface RouteGuardProps {
  children: React.ReactNode
  customPermissions?: boolean // 是否使用自定义权限配置而不是默认配置
}

export default function RouteGuard({ 
  children,
  customPermissions = false
}: RouteGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [authorized, setAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查用户权限
    const checkPermissions = () => {
      // 从localStorage获取用户信息
      const userJson = typeof window !== 'undefined' ? localStorage.getItem('user') : null
      
      // 默认为未登录状态
      let isLoggedIn = false
      let isAdmin = false
      let isEmailVerified = false
      
      // 如果有用户信息，解析并获取权限相关字段
      if (userJson) {
        try {
          const parsedData = JSON.parse(userJson) as any;
          // 提取用户信息，支持不同的数据结构
          const userInfo = parsedData.user_info || parsedData.data?.user_info || parsedData;
          
          isLoggedIn = true;
          isAdmin = userInfo.role === 'admin';
          isEmailVerified = userInfo.emailVerified === true;
          
          console.log('[RouteGuard] Parsed user info:', { isLoggedIn, isAdmin, isEmailVerified, role: userInfo.role, emailVerified: userInfo.emailVerified }); // 调试日志
        } catch (error) {
          console.error('[RouteGuard] Failed to parse user data:', error);
          // 即使解析失败，也保持 isLoggedIn, isAdmin, isEmailVerified 为 false
        }
      }
      
      // 检查当前路径是否有权限访问
      const currentPath = pathname || ''
      const hasAccess = hasPermission(currentPath, isLoggedIn, isAdmin, isEmailVerified)
      
      if (!hasAccess) {
        // 无权访问当前路径
        setAuthorized(false)
        
        if (!isLoggedIn) {
          // 未登录用户重定向到登录页
          router.push('/login')
        } else if (!isEmailVerified && !isAdmin) {
          // 未验证邮箱的非管理员用户，如果尝试访问未授权页面，重定向到仪表板
          router.push('/dashboard')
        } else {
          // 其他情况重定向到仪表板
          router.push('/dashboard')
        }
      } else {
        // 有权访问当前路径
        setAuthorized(true)
      }
      
      setIsLoading(false)
    }

    // 首次加载时检查
    checkPermissions()

    // 监听路由变化
    const handleRouteChange = () => {
      checkPermissions()
    }

    // 添加路由变化监听
    window.addEventListener('popstate', handleRouteChange)

    return () => {
      // 清理监听器
      window.removeEventListener('popstate', handleRouteChange)
    }
  }, [pathname, router])

  // 显示加载状态或已授权的内容
  return isLoading ? <PageLoading /> : (authorized ? <>{children}</> : null)
}
