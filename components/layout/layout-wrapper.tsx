"use client"

import { usePathname } from "next/navigation"
import BackToTop from "@/components/layout/back-to-top"
import Footer from "@/components/layout/footer"
import ClientOnlyHeader from "@/components/layout/client-only-header"
import RouteGuard from "@/components/auth/route-guard"

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isAdminRoute = pathname?.startsWith("/admin") || false

  if (isAdminRoute) {
    return (
      <div className="min-h-screen bg-gray-50">
        <RouteGuard>{children}</RouteGuard>
        <BackToTop />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <ClientOnlyHeader />
      <div className="flex-grow">
        <RouteGuard>{children}</RouteGuard>
      </div>
      <Footer />
      <BackToTop />
    </div>
  )
}
